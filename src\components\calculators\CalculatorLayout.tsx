import Link from "next/link";
import { ReactNode } from "react";
import { <PERSON>Home, FiTool, FiBook, FiLogIn, FiUserPlus } from "react-icons/fi";
import { Calculator } from "lucide-react";
import * as LucideIcons from "lucide-react";
import Header from "@/components/layout/Header";
import { useTheme } from "@/hooks/useTheme";

interface CalculatorLayoutProps {
  children: ReactNode;
  title: string;
  description: string;
  iconName: string;
}

const NAV_LINKS = [
  { href: "/", label: "Home", icon: <FiHome /> },
  { href: "/calculators/all", label: "All Calculators", icon: <Calculator /> },
  { href: "/tools", label: "Tools", icon: <FiTool /> },
  { href: "/blog", label: "Blog", icon: <FiBook /> },
  { href: "/login", label: "Login", icon: <FiLogIn /> },
  { href: "/signup", label: "Sign Up", icon: <FiUserPlus /> },
];

export default function CalculatorLayout({
  children,
  title,
  description,
  iconName,
}: CalculatorLayoutProps) {
  const { theme } = useTheme();
  const isDark = theme === 'dark';
  // Function to render the appropriate icon based on the icon name
  const renderIcon = (iconName: string, className: string = "w-6 h-6") => {
    // Convert kebab-case to PascalCase for Lucide icons
    const pascalCaseName = iconName
      .split("-")
      .map(part => part.charAt(0).toUpperCase() + part.slice(1))
      .join("");

    // Get the icon component from Lucide
    const IconComponent = (LucideIcons as any)[pascalCaseName];

    if (IconComponent) {
      return <IconComponent className={className} />;
    }

    // Fallback to a default icon if the specified icon doesn't exist
    return <LucideIcons.Calculator className={className} />;
  };
  return (
    <div
      className="min-h-screen flex flex-col text-foreground"
      style={{
        background: isDark
          ? 'var(--bg-primary)'
          : 'linear-gradient(to bottom, #dbeafe 0%, #f3f4f6 50%, #ffffff 100%)'
      }}
    >
      <Header />

      <header className="bg-gradient-to-r from-blue-500 to-blue-700 py-8 text-white">
        <div className="container mx-auto px-4">
          <div className="flex flex-col md:flex-row items-center justify-between gap-4">
            <div className="flex items-center gap-3">
              <div className="bg-white/20 p-3 rounded-lg">
                {renderIcon(iconName)}
              </div>
              <div>
                <h1 className="text-2xl md:text-3xl font-bold">{title}</h1>
                <p className="text-white/80 max-w-2xl">{description}</p>
              </div>
            </div>

            <nav className="w-full md:w-auto">
              <ul className="flex flex-wrap justify-center gap-2 md:gap-4">
                {NAV_LINKS.map((link) => (
                  <li key={link.href}>
                    <Link
                      href={link.href}
                      className="flex items-center gap-1 text-white/90 hover:text-white transition-colors px-3 py-2 rounded hover:bg-white/10 text-sm md:text-base"
                    >
                      <span className="text-lg">{link.icon}</span>
                      <span>{link.label}</span>
                    </Link>
                  </li>
                ))}
              </ul>
            </nav>
          </div>
        </div>
      </header>

      <main className="flex-1 container mx-auto px-4 py-8">
        {children}
      </main>

      <footer className="bg-gray-800 text-white py-8 mt-8">
        <div className="container mx-auto px-4">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <div>
              <h2 className="text-lg font-semibold mb-3">PDF Tools</h2>
              <p className="text-gray-400">Your all-in-one solution for PDF management, conversion, and calculations.</p>
            </div>

            <div>
              <h3 className="text-lg font-semibold mb-3">Quick Links</h3>
              <ul className="space-y-2">
                {NAV_LINKS.slice(0, 4).map((link) => (
                  <li key={link.href}>
                    <Link
                      href={link.href}
                      className="text-gray-400 hover:text-white transition-colors flex items-center gap-2"
                    >
                      <span className="text-sm">{link.icon}</span>
                      <span>{link.label}</span>
                    </Link>
                  </li>
                ))}
              </ul>
            </div>

            <div>
              <h3 className="text-lg font-semibold mb-3">Legal</h3>
              <ul className="space-y-2">
                <li>
                  <Link
                    href="/privacy"
                    className="text-gray-400 hover:text-white transition-colors"
                  >
                    Privacy Policy
                  </Link>
                </li>
                <li>
                  <Link
                    href="/terms"
                    className="text-gray-400 hover:text-white transition-colors"
                  >
                    Terms of Service
                  </Link>
                </li>
              </ul>
            </div>
          </div>

          <div className="mt-8 pt-6 border-t border-gray-700 text-center text-gray-500">
            <p>&copy; {new Date().getFullYear()} PDF Tools. All rights reserved.</p>
          </div>
        </div>
      </footer>
    </div>
  );
}
